import { CacheModuleOptions } from '@nestjs/cache-manager';

/**
 * Cache configuration for daemon response caching
 * TTL: 30 minutes (1800 seconds)
 * Store: In-memory (default)
 */
export const cacheConfig: CacheModuleOptions = {
  ttl: 30 * 60 * 1000, // 30 minutes in milliseconds
  max: 1000, // Maximum number of items in cache
  isGlobal: true, // Make cache available globally
};

/**
 * Cache key prefixes for different data types
 */
export const CACHE_KEYS = {
  CRYPTO_STATISTICS: 'crypto:statistics',
  STOCK_STATISTICS: 'stock:statistics',
  CRYPTO_INDICATORS: 'crypto:indicators',
  STOCK_INDICATORS: 'stock:indicators',
} as const;

/**
 * Generate cache key for indicator requests
 */
export function generateIndicatorCacheKey(
  type: 'crypto' | 'stock',
  symbol: string,
  conversionCurrency: string,
): string {
  const prefix = type === 'crypto' ? CACHE_KEYS.CRYPTO_INDICATORS : CACHE_KEYS.STOCK_INDICATORS;
  return `${prefix}:${symbol}:${conversionCurrency}`;
}
