import { Inject, Injectable, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

import {
  DaemonClient,
  DaemonResponse,
  StockDaemonResponse,
  IndicatorValue,
} from '../api/financial.indicator.api';
import FinancialIndicatorRestClient from '../api/financial.indicator.api';
import { CACHE_KEYS, generateIndicatorCacheKey } from '../config/cache.config';

/**
 * Cached wrapper for FinancialIndicatorRestClient
 * Implements 30-minute TTL caching for all daemon responses
 */
@Injectable()
export class CachedFinancialIndicatorService implements DaemonClient {
  private readonly logger = new Logger(CachedFinancialIndicatorService.name);

  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly originalClient: FinancialIndicatorRestClient,
  ) {}

  async getCryptoStatistics(): Promise<DaemonResponse> {
    const cacheKey = CACHE_KEYS.CRYPTO_STATISTICS;

    try {
      // Try to get from cache first
      const cached = await this.cacheManager.get<DaemonResponse>(cacheKey);
      if (cached) {
        this.logger.log(`Cache HIT for ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache MISS for ${cacheKey}, fetching from daemon`);

      // Cache miss - fetch from daemon
      const result = await this.originalClient.getCryptoStatistics();

      // Store in cache with TTL (TTL is configured globally)
      await this.cacheManager.set(cacheKey, result);

      this.logger.log(`Cached result for ${cacheKey}`);
      return result;
    } catch (cacheError) {
      const errorMessage = cacheError instanceof Error ? cacheError.message : String(cacheError);
      this.logger.warn(`Cache error for ${cacheKey}: ${errorMessage}, falling back to daemon`);
      // Fallback to daemon call if cache fails
      return this.originalClient.getCryptoStatistics();
    }
  }

  async getCryptoIndicators(
    symbol: string,
    conversionCurrency: string,
  ): Promise<IndicatorValue[]> {
    const cacheKey = generateIndicatorCacheKey('crypto', symbol, conversionCurrency);

    try {
      // Try to get from cache first
      const cached = await this.cacheManager.get<IndicatorValue[]>(cacheKey);
      if (cached) {
        this.logger.log(`Cache HIT for ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache MISS for ${cacheKey}, fetching from daemon`);

      // Cache miss - fetch from daemon
      const result = await this.originalClient.getCryptoIndicators(symbol, conversionCurrency);

      // Store in cache with TTL
      await this.cacheManager.set(cacheKey, result);

      this.logger.log(`Cached result for ${cacheKey}`);
      return result;
    } catch (cacheError) {
      const errorMessage = cacheError instanceof Error ? cacheError.message : String(cacheError);
      this.logger.warn(`Cache error for ${cacheKey}: ${errorMessage}, falling back to daemon`);
      // Fallback to daemon call if cache fails
      return this.originalClient.getCryptoIndicators(symbol, conversionCurrency);
    }
  }

  async getStockStatistics(): Promise<StockDaemonResponse> {
    const cacheKey = CACHE_KEYS.STOCK_STATISTICS;

    try {
      // Try to get from cache first
      const cached = await this.cacheManager.get<StockDaemonResponse>(cacheKey);
      if (cached) {
        this.logger.log(`Cache HIT for ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache MISS for ${cacheKey}, fetching from daemon`);

      // Cache miss - fetch from daemon
      const result = await this.originalClient.getStockStatistics();

      // Store in cache with TTL
      await this.cacheManager.set(cacheKey, result);

      this.logger.log(`Cached result for ${cacheKey}`);
      return result;
    } catch (cacheError) {
      const errorMessage = cacheError instanceof Error ? cacheError.message : String(cacheError);
      this.logger.warn(`Cache error for ${cacheKey}: ${errorMessage}, falling back to daemon`);
      // Fallback to daemon call if cache fails
      return this.originalClient.getStockStatistics();
    }
  }

  async getStockIndicators(
    symbol: string,
    conversionCurrency: string,
  ): Promise<IndicatorValue[]> {
    const cacheKey = generateIndicatorCacheKey('stock', symbol, conversionCurrency);

    try {
      // Try to get from cache first
      const cached = await this.cacheManager.get<IndicatorValue[]>(cacheKey);
      if (cached) {
        this.logger.log(`Cache HIT for ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache MISS for ${cacheKey}, fetching from daemon`);

      // Cache miss - fetch from daemon
      const result = await this.originalClient.getStockIndicators(symbol, conversionCurrency);

      // Store in cache with TTL
      await this.cacheManager.set(cacheKey, result);

      this.logger.log(`Cached result for ${cacheKey}`);
      return result;
    } catch (cacheError) {
      const errorMessage = cacheError instanceof Error ? cacheError.message : String(cacheError);
      this.logger.warn(`Cache error for ${cacheKey}: ${errorMessage}, falling back to daemon`);
      // Fallback to daemon call if cache fails
      return this.originalClient.getStockIndicators(symbol, conversionCurrency);
    }
  }
}
